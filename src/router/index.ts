import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteLocationNormalized, RouteRecordRaw } from 'vue-router'
import { usePermission } from '@/stores/permission'

const routes: RouteRecordRaw[] = [
  {
    path: '/test-batch-actions',
    name: 'TestBatchActions',
    component: () => import('../views/TestBatchActions.vue'),
    meta: {
      title: '批量操作测试'
    }
  },
  {
    path: '/test-auth-request',
    name: 'TestAuthRequest',
    component: () => import('../views/TestAuthRequest.vue'),
    meta: {
      title: '权限申请测试'
    }
  },
  {
    path: '/',
    name: 'home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      title: 'DataScope - 首页'
    }
  },
  {
    path: '/datasource',
    name: 'DataSourceModule',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: '',
        name: 'datasource-list',
        component: () => import('@/views/datasource/DataSourceView.vue'),
        meta: {
          title: '数据源管理'
        }
      },
      {
        path: 'create',
        name: 'datasource-create',
        component: () => import('@/views/datasource/DataSourceView.vue'),
        meta: {
          title: '创建数据源'
        }
      },
      {
        path: ':id',
        name: 'datasource-detail',
        component: () => import('@/views/datasource/DataSourceView.vue'),
        meta: {
          title: '数据源详情'
        }
      },
      {
        path: 'edit/:id',
        name: 'datasource-edit',
        component: () => import('@/views/datasource/DataSourceView.vue'),
        meta: {
          title: '编辑数据源'
        }
      },
      {
        path: 'search',
        name: 'datasource-search',
        component: () => import('@/views/datasource/DataSourceView.vue'),
        meta: {
          title: '高级搜索'
        }
      },
      {
        path: 'search/results',
        name: 'datasource-search-results',
        component: () => import('@/views/datasource/DataSourceView.vue'),
        meta: {
          title: '搜索结果'
        }
      }
    ]
  },
  {
    path: '/query',
    name: 'QueryModule',
    component: () => import('@/layouts/QueryLayout.vue'),
    children: [
      {
        path: '',
        name: 'QueryHome',
        redirect: { name: 'QueryList' }
      },
      {
        path: 'editor',
        name: 'QueryEditor',
        component: () => import('../views/query/QueryEditor.vue')
      },
      {
        path: 'history',
        name: 'QueryHistory',
        component: () => import('../views/query/QueryHistory.vue')
      },
      {
        path: 'favorites',
        name: 'QueryFavorites',
        component: () => import('../views/query/QueryFavoriteView.vue'),
        meta: {
          title: '收藏的查询'
        }
      },
      {
        path: 'list',
        name: 'QueryList',
        component: () => import('../views/query/QueryListView.vue'),
        meta: {
          title: '查询服务列表'
        }
      },
      {
        path: 'analytics/:id',
        name: 'QueryAnalytics',
        component: () => import('../views/query/QueryAnalytics.vue')
      },
      {
        path: 'detail/:id',
        name: 'QueryDetail',
        component: () => import('../views/query/QueryDetail.vue')
      },
      {
        path: 'detail/:id/version/:versionId',
        name: 'QueryVersionDetails',
        component: () => import('../views/query/QueryVersionDetail.vue'),
        meta: {
          title: '查询版本详情'
        }
      },
      {
        path: 'version/management/:id',
        name: 'VersionManagement',
        component: () => import('../views/query/version/VersionManagementView.vue'),
        meta: {
          title: '版本管理'
        }
      },
      {
        path: 'version/:id',
        name: 'LegacyQueryVersionDetail',
        component: () => import('../views/query/version/QueryDetailView.vue'),
        meta: {
          title: '查询版本详情'
        }
      }
    ]
  },
  {
    path: '/operation',
    name: 'OperationModule',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: '',
        name: 'OperationList',
        component: () => import('../views/operation/OperationList.vue'),
        meta: {
          title: '操作服务列表'
        }
      },
      {
        path: 'create',
        name: 'OperationCreate',
        component: () => import('../views/operation/OperationEdit.vue'),
        meta: {
          title: '创建操作'
        }
      },
      {
        path: 'edit/:id',
        name: 'OperationEdit',
        component: () => import('../views/operation/OperationEdit.vue'),
        meta: {
          title: '编辑操作'
        }
      },
      {
        path: 'detail/:id',
        name: 'OperationDetail',
        component: () => import('../views/operation/OperationDetail.vue'),
        meta: {
          title: '操作详情'
        }
      },
      {
        path: 'history',
        name: 'OperationHistory',
        component: () => import('../views/operation/OperationHistory.vue'),
        meta: {
          title: '操作历史'
        }
      }
    ]
  },

  {
    path: '/integration',
    name: 'IntegrationModule',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: '',
        name: 'IntegrationList',
        component: () => import('../views/integration/IntegrationList.vue')
      },
      {
        path: 'create',
        name: 'IntegrationCreate',
        component: () => import('../views/integration/IntegrationEditContainer.vue')
      },
      {
        path: 'edit/:id',
        name: 'IntegrationEdit',
        component: () => import('../views/integration/IntegrationEditContainer.vue')
      },
      {
        path: 'simple/create',
        name: 'SimpleIntegrationCreate',
        redirect: '/integration/create'
      },
      {
        path: 'simple/edit/:id',
        name: 'SimpleIntegrationEdit',
        redirect: '/integration/edit/:id'
      },
      {
        path: 'preview/:id',
        name: 'IntegrationPreview',
        component: () => import('../views/integration/IntegrationPreview.vue')
      },
      {
        path: 'preview/:id/:type',
        name: 'IntegrationPreviewWithType',
        component: () => import('../views/integration/IntegrationPreview.vue')
      }
    ]
  },
  {
    path: '/pages',
    name: 'PageModule',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: {
      title: '系统页面'
    },
    children: [
      {
        path: '',
        name: 'PageList',
        component: () => import('@/views/pages/PageList.vue'),
        meta: {
          title: '页面列表'
        }
      },
      {
        path: 'create',
        name: 'PageCreate',
        component: () => import('@/views/pages/PageEdit.vue'),
        meta: {
          title: '创建页面'
        }
      },
      {
        path: 'edit/:id',
        name: 'PageEdit',
        component: () => import('@/views/pages/PageEdit.vue'),
        meta: {
          title: '编辑页面'
        }
      },
      {
        path: 'view/:id',
        name: 'PageView',
        redirect: '/page-service-system/viewer/:id'
      }
    ]
  },
  {
    path: '/examples',
    name: 'Examples',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: '',
        name: 'ExamplesIndex',
        component: () => import('../views/examples/ExamplesIndex.vue')
      },
      {
        path: 'table',
        name: 'TableExample',
        component: () => import('../views/examples/TableExample.vue')
      },
      {
        path: 'form',
        name: 'FormExample',
        component: () => import('../views/examples/FormExample.vue')
      },
      {
        path: 'message',
        name: 'MessageExample',
        component: () => import('../views/examples/MessageExample.vue')
      },
      {
        path: 'loading',
        name: 'LoadingExample',
        component: () => import('../views/examples/LoadingExample.vue')
      },
      {
        path: 'modal',
        name: 'ModalExample',
        component: () => import('../views/examples/ModalExample.vue')
      },
      {
        path: 'test',
        name: 'TestView',
        component: () => import('../views/examples/TestView.vue')
      },
      {
        path: 'message-demo',
        name: 'MessageDemo',
        component: () => import('../views/examples/MessageDemo.vue'),
        meta: {
          title: '消息服务演示'
        }
      },
      {
        path: 'enum',
        name: 'EnumManagementExample',
        component: () => import('@/examples/CreateEnumExample.vue'),
        meta: {
          title: '枚举管理示例'
        }
      }
    ]
  },
  // 捕获所有未匹配的路由并重定向到首页
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes
})

router.beforeEach(async (_to, _from, next) => {
  const { hasBeenFetched, fetchInterfacePermission } = usePermission()
  if (!hasBeenFetched) {
    await fetchInterfacePermission()
  }
  next()
})

export default router
